import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SunMoonCombinedWidget extends StatelessWidget {
  final DateTime? sunrise;
  final DateTime? sunset;
  final DateTime? moonrise;
  final DateTime? moonset;

  const SunMoonCombinedWidget({
    super.key,
    this.sunrise,
    this.sunset,
    this.moonrise,
    this.moonset,
  });

  Widget _buildTimeItem(String label, DateTime? time, Color iconColor, IconData directionIcon, BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(6),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 9,
                color: iconColor,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 1),
            time == null
                ? const SizedBox(
                    width: 8,
                    height: 8,
                    child: CircularProgressIndicator(strokeWidth: 1),
                  )
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        directionIcon,
                        size: 8,
                        color: iconColor,
                      ),
                      Text(
                        timeFormatter(time),
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 0),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFF8E1), Color(0xFFE8EAF6)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Sun Section
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.wb_sunny,
                      size: 14,
                      color: Colors.orange,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Sun',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    _buildTimeItem(
                      AppLocalizations.of(context)!.sunrise,
                      sunrise,
                      Colors.orange,
                      Icons.arrow_upward,
                      context,
                    ),
                    const SizedBox(width: 2),
                    _buildTimeItem(
                      AppLocalizations.of(context)!.sunset,
                      sunset,
                      Colors.orange,
                      Icons.arrow_downward,
                      context,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Divider
          Container(
            width: 1,
            height: 50,
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.withValues(alpha: 0.3),
                  Colors.indigo.withValues(alpha: 0.3),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),

          // Moon Section
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.nightlight_round,
                      size: 14,
                      color: Colors.indigo,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Moon',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.indigo,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    _buildTimeItem(
                      AppLocalizations.of(context)!.moonRise,
                      moonrise,
                      Colors.indigo,
                      Icons.arrow_upward,
                      context,
                    ),
                    const SizedBox(width: 2),
                    _buildTimeItem(
                      AppLocalizations.of(context)!.moonSet,
                      moonset,
                      Colors.indigo,
                      Icons.arrow_downward,
                      context,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

List<Widget> getStaticPanchangItem(PanchangDataForThreeDays? panchangData, DateTime currentTime, BuildContext context) {
  if (panchangData == null) {
    return const [
      Center(child: CircularProgressIndicator()),
    ];
  }

  // The logic remains exactly the same as before
  DateTime? sunrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.sunrise.isAfter(currentTime) &&
        sunrise == null) {
      sunrise = dayData.sunrise;
      break;
    }
  }

  DateTime? sunset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (sunrise != null && dayData.sunset.isAfter(sunrise)) {
      sunset = dayData.sunset;
      break;
    }
  }

  DateTime? moonrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.moonrise.isAfter(currentTime.add(const Duration(hours: 3))) &&
        moonrise == null) {
      moonrise = dayData.moonrise;
      break;
    }
  }

  DateTime? moonset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (moonrise != null && dayData.moonset.isAfter(moonrise)) {
      moonset = dayData.moonset;
      break;
    }
  }

  return [
    SunMoonCombinedWidget(
      sunrise: sunrise,
      sunset: sunset,
      moonrise: moonrise,
      moonset: moonset,
    ),
    Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.brown.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(Icons.calendar_month, color: Colors.brown, size: 20),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppLocalizations.of(context)!.week,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    panchangData.currentDay.weekday,
                    style: Theme.of(context).textTheme.headlineMedium,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  ];
}